{"name": "microsoft-reward-account-reset", "version": "1.0.0", "description": "genspark to OpenAI API proxy using H3 and Playwright", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx src/web-server.js", "build": "node build.js", "start": "node dist/index.js", "docker-build": "npm run build && docker build --progress=plain -t zhezzma/microsoft-reward-account-reset:latest .", "docker-dev": "docker-compose -f docker-compose.yml -f docker-compose.override.yml  up -d --build", "docker-push": "docker push zhezzma/microsoft-reward-account-reset:latest"}, "keywords": ["h3", "playwright", "openai", "proxy"], "author": "", "license": "MIT", "dependencies": {"dotenv": "^17.0.1", "h3": "^2.0.0-beta.1", "playwright": "^1.53.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.10.0", "esbuild": "^0.25.5", "tsx": "^4.20.3"}}