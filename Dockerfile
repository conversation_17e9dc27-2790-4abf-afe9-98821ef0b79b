# 基础镜像：使用 Node.js 23 的 Alpine Linux 版本
FROM node:23-alpine

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    # 基本构建工具
    python3 \
    make \
    g++ \
    # Playwright 依赖
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    # 其他依赖
    gcompat


# 设置 Playwright 的环境变量
ENV PLAYWRIGHT_BROWSERS_PATH=/usr/bin
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
ENV PLAYWRIGHT_EXECUTABLE_PATH=/usr/bin/chromium-browser
ENV PLAYWRIGHT_SKIP_BROWSER_VALIDATION=1

# 创建用户和基础目录结构（在安装依赖之前）
RUN addgroup -g 1001 nodejs && \
    adduser -u 1001 -G nodejs -h /home/<USER>
    mkdir -p /app/screenshots /app/logs && \
    chown -R nodejs:nodejs /home/<USER>/app && \
    chmod -R 777 /app/screenshots /app/logs /home/<USER>

# 复制依赖文件并安装
COPY package*.json  ./
RUN npm ci --only=production

USER nodejs
# 切换到非 root 用户
ENV HOME=/home/<USER>

# 复制应用文件（变化频率最高，放在最后）
COPY --chown=nodejs:nodejs dist/ ./dist/
COPY --chown=nodejs:nodejs public/ ./public/

# 声明容器要暴露的端口
EXPOSE 7860
ENV PORT=7860

# 启动应用
CMD ["node", "dist/index.js"]