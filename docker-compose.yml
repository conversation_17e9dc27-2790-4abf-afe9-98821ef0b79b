version: '3.8'

services:
  app:
    build: .
    ports:
      - "7860:7860"
    environment:
      - NODE_ENV=production
      - PORT=7860
      - HOST=0.0.0.0
    # DNS 配置
    dns:
      - *******
      - *******
      - *******
    # 网络配置
    networks:
      - app-network
    # 添加额外的主机映射（如果需要）
    extra_hosts:
      - "rewards.bing.com:************"
      - "rewards.microsoft.com:************"
      - "account.microsoft.com:************"
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # 重启策略
    restart: unless-stopped
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
