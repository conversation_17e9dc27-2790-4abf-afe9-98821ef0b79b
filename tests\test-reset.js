// 测试重置路由的简单脚本
import config from '../src/config.js';

const testAccounts = [
{
    "id": 69,
    "email": "<EMAIL>",
    "password": "dkph7652676",
    "proofEmail": "",
    "createDatetime": "2025-08-05 21:16:12",
    "resetStatus": 0,
    "resetDatetime": "2025-08-06 20:22:12",
    "resetFailMsg": "Failed after 4 attempts: page.waitForURL: Timeout 5000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://login.live.com/**\" until \"load\"\n============================================================",
    "initStatus": 0,
    "initDatetime": "",
    "initFailMsg": ""
  },
  {
    "id": 70,
    "email": "<EMAIL>",
    "password": "dkkt306041",
    "proofEmail": "",
    "createDatetime": "2025-08-05 21:16:12",
    "resetStatus": 0,
    "resetDatetime": "2025-08-06 20:22:12",
    "resetFailMsg": "Failed after 4 attempts: page.waitForURL: Timeout 5000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://login.live.com/**\" until \"load\"\n============================================================",
    "initStatus": 0,
    "initDatetime": "",
    "initFailMsg": ""
  },
  {
    "id": 71,
    "email": "<EMAIL>",
    "password": "kaesorq1002",
    "proofEmail": "",
    "createDatetime": "2025-08-05 21:16:12",
    "resetStatus": 0,
    "resetDatetime": "2025-08-06 20:22:12",
    "resetFailMsg": "Failed after 4 attempts: page.waitForURL: Timeout 5000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://login.live.com/**\" until \"load\"\n============================================================",
    "initStatus": 0,
    "initDatetime": "",
    "initFailMsg": ""
  },
  {
    "id": 72,
    "email": "<EMAIL>",
    "password": "ftqw62056",
    "proofEmail": "",
    "createDatetime": "2025-08-05 21:16:12",
    "resetStatus": 0,
    "resetDatetime": "2025-08-07 16:23:16",
    "resetFailMsg": "Failed after 4 attempts: page.waitForURL: Timeout 5000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://login.live.com/**\" until \"load\"\n============================================================",
    "initStatus": 0,
    "initDatetime": "",
    "initFailMsg": ""
  },
  {
    "id": 73,
    "email": "<EMAIL>",
    "password": "pybmez18835",
    "proofEmail": "",
    "createDatetime": "2025-08-05 21:16:12",
    "resetStatus": 0,
    "resetDatetime": "2025-08-07 16:23:16",
    "resetFailMsg": "Failed after 4 attempts: page.waitForURL: Timeout 5000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://login.live.com/**\" until \"load\"\n============================================================",
    "initStatus": 0,
    "initDatetime": "",
    "initFailMsg": ""
  },
];

async function testResetEndpoint() {
    try {
        //https://89dbbd46cf2039858a0f44184c36603f62447fe4-7860.dstack-prod8.phala.network/reset-account
        //http://localhost:3096/reset-account
        const response = await fetch('https://89dbbd46cf2039858a0f44184c36603f62447fe4-7860.dstack-prod8.phala.network/reset-account', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.app.token}`
            },
            body: JSON.stringify(testAccounts)
        });

        const result = await response.json();
        console.log('Response Status:', response.status);
        console.log('Response:', JSON.stringify(result, null, 2));
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

// 执行测试
console.log('Testing reset endpoint...');
testResetEndpoint();
