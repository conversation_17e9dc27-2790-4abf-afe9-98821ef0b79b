// @ts-check
import { getQuery, setHeader } from 'h3';
import { getScreenshotList, getScreenshotContent } from '../utils/screenshotHelper.js';

/**
 * @typedef {Object} ScreenshotInfo
 * @property {string} name - 截图文件名
 * @property {string} email - 邮箱地址
 * @property {string} timestamp - 时间戳
 * @property {number} size - 文件大小
 * @property {Date} mtime - 修改时间
 */

/**
 * @typedef {Object} ScreenshotListResponse
 * @property {boolean} success - 操作是否成功
 * @property {string} message - 响应消息
 * @property {ScreenshotInfo[]} screenshots - 截图列表
 * @property {number} total - 截图总数
 */

/**
 * 获取截图列表的处理函数
 * @param {import('h3').H3Event} event - H3 事件对象
 * @returns {Promise<ScreenshotListResponse>} 截图列表响应
 */
export async function getScreenshotsHandler(event) {
    try {
        const screenshots = await getScreenshotList();
        
        return {
            success: true,
            message: 'Screenshots retrieved successfully',
            screenshots: screenshots,
            total: screenshots.length
        };
    } catch (error) {
        console.error('Get screenshots error:', error);
        return {
            success: false,
            message: `Failed to get screenshots: ${/** @type {Error} */(error).message}`,
            screenshots: [],
            total: 0
        };
    }
}

/**
 * 获取单个截图文件的处理函数
 * @param {import('h3').H3Event} event - H3 事件对象
 * @returns {Promise<Buffer|Object>} 截图文件内容或错误响应
 */
export async function getScreenshotHandler(event) {
    try {
        const query = getQuery(event);
        const filename = query.filename;
        
        if (!filename || typeof filename !== 'string') {
            setHeader(event, 'Content-Type', 'application/json');
            return {
                success: false,
                message: 'Filename parameter is required'
            };
        }

        // 验证文件名格式，防止路径遍历攻击
        if (!filename.match(/^error_[a-zA-Z0-9._-]+\.png$/)) {
            setHeader(event, 'Content-Type', 'application/json');
            return {
                success: false,
                message: 'Invalid filename format'
            };
        }

        const screenshotContent = await getScreenshotContent(filename);
        
        // 设置响应头为图片类型
        setHeader(event, 'Content-Type', 'image/png');
        setHeader(event, 'Content-Disposition', `inline; filename="${filename}"`);
        setHeader(event, 'Cache-Control', 'public, max-age=3600'); // 缓存1小时
        
        return screenshotContent;
    } catch (error) {
        console.error('Get screenshot error:', error);
        setHeader(event, 'Content-Type', 'application/json');
        return {
            success: false,
            message: `Failed to get screenshot: ${/** @type {Error} */(error).message}`
        };
    }
}

/**
 * 生成截图查看页面的HTML
 * @param {ScreenshotInfo[]} screenshots - 截图列表
 * @returns {string} HTML内容
 */
function generateScreenshotViewHTML(screenshots) {
    const screenshotItems = screenshots.map(screenshot => `
        <div class="screenshot-item">
            <div class="screenshot-info">
                <h3>${screenshot.name}</h3>
                <p><strong>邮箱:</strong> ${screenshot.email}</p>
                <p><strong>时间:</strong> ${new Date(screenshot.mtime).toLocaleString('zh-CN')}</p>
                <p><strong>大小:</strong> ${(screenshot.size / 1024).toFixed(2)} KB</p>
            </div>
            <div class="screenshot-preview">
                <img src="/api/screenshot?filename=${screenshot.name}" alt="${screenshot.name}" loading="lazy">
            </div>
        </div>
    `).join('');

    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误截图查看器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            color: #333;
        }
        .stats {
            margin-top: 10px;
            color: #666;
        }
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        .screenshot-item {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .screenshot-item:hover {
            transform: translateY(-2px);
        }
        .screenshot-info {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        .screenshot-info h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }
        .screenshot-info p {
            margin: 5px 0;
            font-size: 12px;
            color: #666;
        }
        .screenshot-preview {
            padding: 15px;
            text-align: center;
        }
        .screenshot-preview img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .screenshot-preview img:hover {
            transform: scale(1.02);
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .empty-state h2 {
            color: #666;
            margin-bottom: 10px;
        }
        .empty-state p {
            color: #999;
        }
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }
        .modal-content {
            margin: auto;
            display: block;
            width: 90%;
            max-width: 1200px;
            max-height: 90%;
            object-fit: contain;
        }
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: #bbb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ 错误截图查看器</h1>
            <div class="stats">
                总计 ${screenshots.length} 个错误截图 (最多保存20个)
                <button class="refresh-btn" onclick="location.reload()">🔄 刷新</button>
            </div>
        </div>
        
        ${screenshots.length > 0 ? `
            <div class="screenshot-grid">
                ${screenshotItems}
            </div>
        ` : `
            <div class="empty-state">
                <h2>暂无错误截图</h2>
                <p>当账号重置失败时，系统会自动保存错误截图</p>
            </div>
        `}
    </div>

    <!-- 模态框 -->
    <div id="imageModal" class="modal">
        <span class="close">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>

    <script>
        // 图片点击放大功能
        const modal = document.getElementById('imageModal');
        const modalImg = document.getElementById('modalImage');
        const images = document.querySelectorAll('.screenshot-preview img');
        const closeBtn = document.querySelector('.close');

        images.forEach(img => {
            img.onclick = function() {
                modal.style.display = 'block';
                modalImg.src = this.src;
            }
        });

        closeBtn.onclick = function() {
            modal.style.display = 'none';
        }

        modal.onclick = function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                modal.style.display = 'none';
            }
        });
    </script>
</body>
</html>`;
}

/**
 * 截图查看页面的处理函数
 * @param {import('h3').H3Event} event - H3 事件对象
 * @returns {Promise<string>} HTML页面内容
 */
export async function screenshotViewHandler(event) {
    try {
        const screenshots = await getScreenshotList();
        const html = generateScreenshotViewHTML(screenshots);
        
        setHeader(event, 'Content-Type', 'text/html; charset=utf-8');
        return html;
    } catch (error) {
        console.error('Screenshot view error:', error);
        setHeader(event, 'Content-Type', 'text/html; charset=utf-8');
        return `
<!DOCTYPE html>
<html>
<head>
    <title>错误</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>加载截图时出错</h1>
    <p>${/** @type {Error} */(error).message}</p>
</body>
</html>`;
    }
}
