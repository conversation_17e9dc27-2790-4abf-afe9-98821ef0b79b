// @ts-check
import { saveErrorScreenshot, getScreenshotList } from '../src/utils/screenshotHelper.js';
import BrowserManager from '../src/utils/browser.js';

/**
 * 测试截图功能
 */
async function testScreenshotFunctionality() {
    console.log('🧪 开始测试截图功能...');

    try {
        console.log('📦 获取浏览器实例...');
        const browser = await BrowserManager.getInstance();
        console.log('✅ 浏览器实例获取成功');
        const context = await browser.newContext();
        console.log('✅ 浏览器上下文创建成功');

        const page = await context.newPage();
        console.log('✅ 页面创建成功');

        // 访问一个测试页面
        console.log('🌐 访问测试页面...');
        await page.goto('https://baidu.com');
        await page.waitForLoadState('networkidle');
        console.log('✅ 页面加载完成');

        // 模拟保存错误截图
        const testEmail = '<EMAIL>';
        console.log(`📸 为邮箱 ${testEmail} 保存测试截图...`);

        const screenshotPath = await saveErrorScreenshot(page, testEmail);
        console.log(`✅ 截图已保存: ${screenshotPath}`);

        // 获取截图列表
        console.log('📋 获取截图列表...');
        const screenshots = await getScreenshotList();
        console.log(`📋 当前截图列表 (${screenshots.length} 个):`);

        screenshots.forEach((screenshot, index) => {
            console.log(`  ${index + 1}. ${screenshot.name}`);
            console.log(`     邮箱: ${screenshot.email}`);
            console.log(`     时间: ${new Date(screenshot.mtime).toLocaleString('zh-CN')}`);
            console.log(`     大小: ${(screenshot.size / 1024).toFixed(2)} KB`);
            console.log('');
        });

        // 清理
        console.log('🧹 清理资源...');
        await page.close();
        await context.close();
        await BrowserManager.closeBrowser();

        console.log('✅ 截图功能测试完成!');
        console.log('🌐 访问 http://localhost:3096/screenshots 查看截图');

    } catch (error) {
        console.error('❌ 测试失败:', error);
        console.error('错误详情:', error.stack);

        // 确保浏览器被关闭
        try {
            await BrowserManager.closeBrowser();
        } catch (cleanupError) {
            console.error('清理浏览器时出错:', cleanupError);
        }
    }
}

// 运行测试
testScreenshotFunctionality();
