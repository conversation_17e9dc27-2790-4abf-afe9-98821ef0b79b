// @ts-check
import { promises as fs } from 'fs';
import path from 'path';

/**
 * 截图保存工具类
 */
class ScreenshotHelper {
    constructor() {
        this.screenshotsDir = path.join(process.cwd(), 'screenshots');
        this.maxScreenshots = 20;
    }

    /**
     * 确保截图目录存在
     * @returns {Promise<void>}
     */
    async ensureScreenshotsDir() {
        try {
            await fs.access(this.screenshotsDir);
        } catch {
            await fs.mkdir(this.screenshotsDir, { recursive: true });
        }
    }

    /**
     * 生成截图文件名
     * @param {string} email - 邮箱地址
     * @returns {string} 截图文件名
     */
    generateScreenshotName(email) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const emailPrefix = email.split('@')[0];
        return `error_${emailPrefix}_${timestamp}.png`;
    }

    /**
     * 获取所有截图文件信息
     * @returns {Promise<Array<{name: string, path: string, mtime: Date}>>}
     */
    async getScreenshotFiles() {
        await this.ensureScreenshotsDir();
        
        try {
            const files = await fs.readdir(this.screenshotsDir);
            const screenshotFiles = files.filter(file => file.endsWith('.png') && file.startsWith('error_'));
            
            const fileInfos = await Promise.all(
                screenshotFiles.map(async (file) => {
                    const filePath = path.join(this.screenshotsDir, file);
                    const stats = await fs.stat(filePath);
                    return {
                        name: file,
                        path: filePath,
                        mtime: stats.mtime
                    };
                })
            );

            // 按修改时间排序，最新的在前
            return fileInfos.sort((a, b) => b.mtime.getTime() - a.mtime.getTime());
        } catch (error) {
            console.error('Error reading screenshots directory:', error);
            return [];
        }
    }

    /**
     * 清理旧的截图文件，保持最多20个
     * @returns {Promise<void>}
     */
    async cleanupOldScreenshots() {
        const files = await this.getScreenshotFiles();
        
        if (files.length > this.maxScreenshots) {
            const filesToDelete = files.slice(this.maxScreenshots);
            
            for (const file of filesToDelete) {
                try {
                    await fs.unlink(file.path);
                    console.log(`Deleted old screenshot: ${file.name}`);
                } catch (error) {
                    console.error(`Failed to delete screenshot ${file.name}:`, error);
                }
            }
        }
    }

    /**
     * 保存错误截图
     * @param {import('playwright').Page} page - Playwright页面对象
     * @param {string} email - 邮箱地址
     * @returns {Promise<string>} 截图文件路径
     */
    async saveErrorScreenshot(page, email) {
        await this.ensureScreenshotsDir();
        
        const filename = this.generateScreenshotName(email);
        const screenshotPath = path.join(this.screenshotsDir, filename);
        
        try {
            await page.screenshot({
                path: screenshotPath,
                fullPage: true,
                type: 'png'
            });
            
            console.log(`Screenshot saved: ${screenshotPath}`);
            
            // 清理旧截图
            await this.cleanupOldScreenshots();
            
            return screenshotPath;
        } catch (error) {
            console.error(`Failed to save screenshot for ${email}:`, error);
            throw error;
        }
    }

    /**
     * 获取截图列表信息
     * @returns {Promise<Array<{name: string, email: string, timestamp: string, size: number, mtime: Date}>>}
     */
    async getScreenshotList() {
        const files = await this.getScreenshotFiles();
        
        return Promise.all(
            files.map(async (file) => {
                const stats = await fs.stat(file.path);
                const nameParts = file.name.replace('.png', '').split('_');
                const email = nameParts.length > 2 ? nameParts[1] : 'unknown';
                const timestamp = nameParts.slice(2).join('_');
                
                return {
                    name: file.name,
                    email: email,
                    timestamp: timestamp,
                    size: stats.size,
                    mtime: file.mtime
                };
            })
        );
    }

    /**
     * 获取截图文件内容
     * @param {string} filename - 截图文件名
     * @returns {Promise<Buffer>} 截图文件内容
     */
    async getScreenshotContent(filename) {
        const screenshotPath = path.join(this.screenshotsDir, filename);
        
        try {
            await fs.access(screenshotPath);
            return await fs.readFile(screenshotPath);
        } catch (error) {
            throw new Error(`Screenshot not found: ${filename}`);
        }
    }
}

// 创建单例实例
const screenshotHelper = new ScreenshotHelper();

/**
 * 保存错误截图的便捷函数
 * @param {import('playwright').Page} page - Playwright页面对象
 * @param {string} email - 邮箱地址
 * @returns {Promise<string>} 截图文件路径
 */
export async function saveErrorScreenshot(page, email) {
    return await screenshotHelper.saveErrorScreenshot(page, email);
}

/**
 * 获取截图列表的便捷函数
 * @returns {Promise<Array<{name: string, email: string, timestamp: string, size: number, mtime: Date}>>}
 */
export async function getScreenshotList() {
    return await screenshotHelper.getScreenshotList();
}

/**
 * 获取截图内容的便捷函数
 * @param {string} filename - 截图文件名
 * @returns {Promise<Buffer>} 截图文件内容
 */
export async function getScreenshotContent(filename) {
    return await screenshotHelper.getScreenshotContent(filename);
}

export default screenshotHelper;
