import { getRequestURL } from 'h3';
import config from '../config.js';


/**
 * CORS (跨域资源共享) 中间件。
 * 使用 H3 内置的 CORS 处理功能。
 * @param {import('h3').H3Event} event - H3 事件对象。
 * @param {import('h3').next} next - 下一个中间件函数。
 * @returns {Response|void} 如果是 OPTIONS 预检请求，则返回处理结果。
 */

export async function authMiddleware(event, next) {
  // 跳过健康检查端点的认证
  const url = getRequestURL(event);
  if (url.pathname === '/health' || url.pathname === '/screenshots') {
    return await next();
  }

  const authHeader = event.node.req.headers.authorization;
  if (!authHeader) {
    const error = new Error('Unauthorized: Missing Authorization header.');
    error.statusCode = 401;
    throw error;
  }

  const [authType, token] = authHeader.split(' ');
  if (authType !== 'Bearer' || !token) {
    const error = new Error('Unauthorized: Invalid Authorization header format. Expected: Bearer <token>.');
    error.statusCode = 401;
    throw error;
  }

  if (token !== config.app.token) {
    const error = new Error('Unauthorized: Invalid API token.');
    error.statusCode = 401;
    throw error;
  }

  const rawBody = await next();
  // [intercept response]
  return rawBody;
}

