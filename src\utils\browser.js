import { chromium } from 'playwright';
import config from '../config.js';

/**
 * 浏览器管理器类
 * 提供单例模式的浏览器实例管理，确保应用程序中只有一个浏览器实例
 */
class BrowserManager {
    /** @type {import('playwright').Browser | null} 浏览器实例 */
    static instance = null;

    /** @type {boolean} 是否正在初始化浏览器 */
    static isInitializing = false;

    /** @type {Promise<import('playwright').Browser> | null} 初始化Promise */
    static initPromise = null;

    /**
     * 获取浏览器实例（单例模式）
     * 如果实例不存在或已断开连接，则创建新实例
     * @returns {Promise<import('playwright').Browser>} 浏览器实例
     */
    static async getInstance() {
        if (this.instance && this.instance.isConnected()) {
            return this.instance;
        }

        if (this.isInitializing) {
            return this.initPromise;
        }

        this.isInitializing = true;
        this.initPromise = this.initializeBrowser();

        try {
            this.instance = await this.initPromise;
            return this.instance;
        } finally {
            this.isInitializing = false;
            this.initPromise = null;
        }
    }

    /**
     * 初始化浏览器实例
     * 使用配置文件中的设置启动 Chromium 浏览器
     * @returns {Promise<import('playwright').Browser>} 新的浏览器实例
     */
    static async initializeBrowser() {
        return chromium.launch({
            headless: config.browser.headless,
            args: [
                ...config.browser.args
            ],
            executablePath: config.browser.executablePath,
        });
    }

    /**
     * 关闭浏览器实例
     * 安全地关闭浏览器并清理实例引用
     * @returns {Promise<void>}
     */
    static async closeBrowser() {
        if (this.instance) {
            await this.instance.close();
            this.instance = null;
        }
    }
}

export default BrowserManager;