// 测试重置路由的简单脚本
import config from '../src/config.js';

const testAccounts = [
  {
    "id": 2,
    "email": "@outlook.com",
    "password": "111@microsoft",
    "proofEmail": "<EMAIL>",
    "createDatetime": "2025-07-31 22:43:07",
    "resetStatus": 1,
    "resetDatetime": "2025-07-31 22:55:30",
    "resetFailMsg": "",
    "initStatus": 0,
    "initDatetime": "",
    "initFailMsg": ""
  },
];

async function testResetEndpoint() {
    try {
        const response = await fetch('http://localhost:3096/init-account', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.app.token}`
            },
            body: JSON.stringify(testAccounts)
        });

        const result = await response.json();
        console.log('Response Status:', response.status);
        console.log('Response:', JSON.stringify(result, null, 2));
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

// 执行测试
console.log('Testing init endpoint...');
testResetEndpoint();
